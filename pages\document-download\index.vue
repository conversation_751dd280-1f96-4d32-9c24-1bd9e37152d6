<template>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">

    <!-- Header -->
    <div class="mb-8">
      <div class="bg-gray-100 rounded-2xl p-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-2">Download Documents</h1>
        <p class="text-gray-700">Download documents for patients</p>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="text-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      <p class="mt-2 text-gray-500">Loading documents...</p>
    </div>

    <!-- Not Logged In State -->
    <div v-else-if="!isLoggedIn" class="text-center py-12">
      <div class="mx-auto h-12 w-12 text-gray-400 mb-4">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-1a2 2 0 00-2-2H6a2 2 0 00-2 2v1a2 2 0 002 2zM11 5a2 2 0 012 2v4a2 2 0 01-2 2H9a2 2 0 01-2-2V7a2 2 0 012-2h2z"></path>
        </svg>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">Authentication Required</h3>
      <p class="text-gray-500 mb-6">You need to be logged in to access document downloads.</p>
      <NuxtLink
        to="/login"
        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium transition-colors"
      >
        Go to Login
      </NuxtLink>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error Loading Documents</h3>
          <p class="mt-1 text-sm text-red-700">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Documents List -->
    <div v-else-if="documents && documents.length > 0" class="bg-white rounded-lg shadow-md">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">Available Documents</h2>
      </div>
      <div class="divide-y divide-gray-200">
        <div
          v-for="document in documents"
          :key="document.id || document.$id"
          class="px-6 py-4 hover:bg-gray-50"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-900">{{ document.name || document.filename || 'Document' }}</p>
                <p class="text-sm text-gray-500">{{ document.type || document.procedure || 'Unknown type' }}</p>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="downloadDocument(document)"
                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Download
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Documents State -->
    <div v-else class="text-center py-12">
      <div class="mx-auto h-12 w-12 text-gray-400">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
      </div>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No documents for download</h3>
      <p class="mt-1 text-sm text-gray-500">There are currently no documents available for download.</p>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// Make this page accessible without authentication like other login pages
definePageMeta({
  // No middleware - allow direct access
})

// Get current user
const { currentUser, isLoggedIn, checkAuth } = useAuth()

// State management
const documents = ref([])
const loading = ref(true)
const error = ref('')

// Load documents for download
const loadDocuments = async () => {
  if (!currentUser.value) {
    error.value = 'User not authenticated'
    loading.value = false
    return
  }

  try {
    loading.value = true
    error.value = ''

    const response = await $fetch(`/api/documents-download/${currentUser.value.$id}`)

    // Handle different response structures
    if (Array.isArray(response)) {
      documents.value = response
    } else if (response && Array.isArray(response.documents)) {
      documents.value = response.documents
    } else if (response && response.data && Array.isArray(response.data)) {
      documents.value = response.data
    } else {
      documents.value = []
    }

  } catch (err) {
    console.error('Error loading documents:', err)
    error.value = err.message || 'Failed to load documents'
    documents.value = []
  } finally {
    loading.value = false
  }
}

// Download document function
const downloadDocument = async (document) => {
  try {
    // This will depend on how your Go backend provides download URLs
    // You might need to call another endpoint to get the download URL
    console.log('Downloading document:', document)

    // Example implementation - adjust based on your Go backend response
    if (document.downloadUrl) {
      window.open(document.downloadUrl, '_blank')
    } else if (document.id) {
      // Call another endpoint to get download URL
     // window.open(`/api/document-download-url/${document.id}`, '_blank')
    } else {
      alert('Download URL not available for this document')
    }
  } catch (err) {
    console.error('Error downloading document:', err)
    alert('Failed to download document')
  }
}

// Load documents when component mounts
onMounted(async () => {
  await checkAuth()
  if (isLoggedIn.value && currentUser.value) {
    await loadDocuments()
  }
})
</script>