<template>
  <header class="bg-white shadow-lg">
    <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- Header for authenticated non-dashboard pages - Logo left, Back button right -->
      <div v-if="shouldShowBackButton" class="flex justify-between items-center h-16">
        <!-- Logo/Site Name - Left Side -->
        <div class="flex-shrink-0">
          <button @click="handleLogoClick" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
            <img src="/images/logo.webp" alt="Surgassists Logo" class="h-8 w-auto" />
          </button>
        </div>

        <!-- Back to Dashboard Button - Right Side -->
        <div>
          <NuxtLink
            to="/dashboard"
            class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-full text-base font-medium text-gray-700 bg-white hover:bg-gray-50 hover:text-blue-600 transition-colors shadow-sm"
          >
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back To Dashboard
          </NuxtLink>
        </div>
      </div>

      <!-- Full Header - Show only on /dashboard page -->
      <div v-else-if="isDashboardPage" class="flex justify-between items-center h-16">
        <!-- Logo/Site Name - Left Side -->
        <div class="flex-shrink-0">
          <button @click="handleLogoClick" class="text-2xl font-bold text-blue-600 hover:text-blue-700 transition-colors">
            <img src="/images/logo.webp" alt="Surgassists Logo" class="h-8 w-auto" />
          </button>
        </div>

        <!-- Desktop Menu - Right Side -->
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            <!-- Show user info and logout if logged in -->
            <div v-if="isLoggedIn" class="flex items-center space-x-4">
              <!-- User Profile Circle -->
              <NuxtLink
                to="/profile"
                class="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold hover:opacity-80 transition-opacity cursor-pointer"
                style="background-color: #55acee;"
                :title="`Go to ${userName}'s profile`"
              >
                {{ getUserInitials(userName) }}
              </NuxtLink>

              <!-- Notification Bell -->
               <NuxtLink
                to="/notifications"
                class="w-8 h-8 border border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50 cursor-pointer transition-colors"
              >
              <div class="relative">
                <div class="w-8 h-8 border border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50 cursor-pointer transition-colors">
                  <!-- Bell Icon -->
                  <svg class="w-4 h-4 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                  </svg>

                  <!-- Red notification badge -->
                  <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                    <span class="text-white text-xs font-bold">0</span>
                  </div>
                </div>
              </div>
              </NuxtLink>

              <button
                @click="handleLogout"
                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                :disabled="isLoggingOut"
              >
                <span v-if="isLoggingOut">Logging out...</span>
                <span v-else>Logout</span>
              </button>
            </div>
            <!-- Show login if not logged in -->
            <NuxtLink
              v-else
              to="/login"
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              Login
            </NuxtLink>
          </div>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button
            @click="toggleMobileMenu"
            type="button"
            class="bg-gray-50 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
            aria-controls="mobile-menu"
            :aria-expanded="isMobileMenuOpen"
          >
            <span class="sr-only">Open main menu</span>
            <!-- Hamburger icon when menu is closed -->
            <svg
              v-if="!isMobileMenuOpen"
              class="block h-6 w-6"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
            <!-- X icon when menu is open -->
            <svg
              v-else
              class="block h-6 w-6"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Menu -->
      <div v-show="isMobileMenuOpen" class="md:hidden" id="mobile-menu">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200">
          <!-- Show user info and logout if logged in -->
          <div v-if="isLoggedIn" class="space-y-2">
            <div class="flex items-center justify-between px-3 py-2">
              <!-- Mobile User Profile Circle -->
              <NuxtLink
                to="/profile"
                class="flex items-center space-x-3 hover:opacity-80 transition-opacity"
              >
                <div
                  class="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold"
                  style="background-color: #55acee;"
                >
                  {{ getUserInitials(userName) }}
                </div>
                <span class="text-gray-700 text-base font-medium">{{ userName }}</span>
              </NuxtLink>

              <!-- Mobile Notification Bell -->
              <div class="relative">
                <div class="w-8 h-8 border border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50 cursor-pointer transition-colors">
                  <!-- Bell Icon -->
                  <svg class="w-4 h-4 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                  </svg>

                  <!-- Red notification badge -->
                  <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                    <span class="text-white text-xs font-bold">0</span>
                  </div>
                </div>
              </div>
            </div>
            <button
              @click="handleLogout"
              class="bg-red-600 hover:bg-red-700 text-white block w-full px-3 py-2 rounded-md text-base font-medium transition-colors text-left"
              :disabled="isLoggingOut"
            >
              <span v-if="isLoggingOut">Logging out...</span>
              <span v-else>Logout</span>
            </button>
          </div>
          <!-- Show login if not logged in -->
          <NuxtLink
            v-else
            to="/login"
            @click="closeMobileMenu"
            class="bg-blue-600 hover:bg-blue-700 text-white block px-3 py-2 rounded-md text-base font-medium transition-colors"
          >
            Login
          </NuxtLink>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// Mobile menu state
const isMobileMenuOpen = ref(false)
const isLoggingOut = ref(false)

// Authentication state using composable
const { isLoggedIn, currentUser, logout } = useAuth()

// Router for navigation
const router = useRouter()
const route = useRoute()

// Check if current page is dashboard
const isDashboardPage = computed(() => {
  return route.path === '/dashboard'
})

// Check if current page should show "Back to Dashboard" button
const shouldShowBackButton = computed(() => {
  // Don't show on dashboard page
  if (route.path === '/dashboard') return false

  // Don't show on auth pages only
  const authPages = ['/login', '/register', '/']
  const isAuthPage = authPages.some(page => route.path === page)
  if (isAuthPage) return false

  // Show on all other pages (including patient consent pages and document-download)
  return true
})

// Computed user name
const userName = computed(() => {
  return currentUser.value?.name || currentUser.value?.email || 'User'
})

// Get user initials for profile circle
const getUserInitials = (name) => {
  if (!name) return 'U'

  const words = name.trim().split(' ')
  if (words.length === 1) {
    // Single word - take first 2 characters
    return words[0].substring(0, 2).toUpperCase()
  } else {
    // Multiple words - take first letter of first 2 words
    return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase()
  }
}

const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
}

const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

// Handle logo click - smart navigation
const handleLogoClick = () => {
  if (isLoggedIn.value) {
    // User is logged in - redirect to dashboard
    router.push('/dashboard')
  } else {
    // User is not logged in - redirect to login
    router.push('/login')
  }
}

// Handle logout
const handleLogout = async () => {
  try {
    isLoggingOut.value = true
    await logout()

    // Close mobile menu if open
    closeMobileMenu()

    // Redirect to home page
    router.push('/')
  } catch (error) {
    console.error('Logout error:', error)
  } finally {
    isLoggingOut.value = false
  }
}

// Close mobile menu when clicking outside (optional enhancement)
const handleClickOutside = (event) => {
  if (isMobileMenuOpen.value && !event.target.closest('#mobile-menu') && !event.target.closest('button')) {
    closeMobileMenu()
  }
}

// Add event listener for clicking outside and check auth status
const { checkAuth } = useAuth()

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  checkAuth()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
